<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok for Business API Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #ff0050, #ff4081, #ff6ec7);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }
        h1 {
            background: linear-gradient(135deg, #ff0050, #ff4081);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: bold;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #ff0050;
            background-color: #f9f9f9;
            border-radius: 0 15px 15px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            border-color: #ff0050;
            outline: none;
        }
        button {
            background: linear-gradient(135deg, #ff0050, #ff4081);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: transform 0.2s;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #ff0050;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .limitation {
            background-color: #ffeaa7;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #d63031;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 TikTok for Business API Setup</h1>
        
        <div class="limitation">
            <strong>⚠️ Important Limitation:</strong> TikTok does not have traditional direct messaging APIs like other platforms.
            This integration uses Unipile API for messaging and focuses on content management, analytics, and engagement through comments and video responses.
        </div>

        <div class="step">
            <h3>🚀 Method 1: Unipile API (Recommended for Messaging)</h3>
            <div class="info">
                <strong>Unipile provides unified access to TikTok messaging with simplified authentication.</strong>
            </div>

            <div class="form-group">
                <label for="unipileApiKey">Unipile API Key:</label>
                <input type="password" id="unipileApiKey" name="unipileApiKey"
                       placeholder="Your Unipile API key">
            </div>

            <button type="button" onclick="connectUnipile()">Connect via Unipile</button>
            <button type="button" onclick="checkUnipileStatus()">Check Connection Status</button>

            <div id="unipileResult"></div>
            <div id="connectionStatus"></div>
        </div>

        <div class="step">
            <h3>📱 Method 2: TikTok API (For Content & Comments)</h3>
            <div class="info">
                <strong>TikTok for Business API for content management and comment interactions.</strong>
            </div>
        </div>

        <div class="step">
            <h3>Step 1: Create TikTok for Business Account</h3>
            <p>1. Go to <a href="https://business.tiktok.com/" target="_blank">TikTok for Business</a></p>
            <p>2. Sign up or log in with your TikTok account</p>
            <p>3. Convert your personal account to a Business account if needed</p>
            <p>4. Complete your business profile setup</p>
        </div>

        <div class="step">
            <h3>Step 2: Apply for TikTok Developer Access</h3>
            <p>1. Visit <a href="https://developers.tiktok.com/" target="_blank">TikTok Developers</a></p>
            <p>2. Apply for developer access (approval required)</p>
            <p>3. Create a new app in the developer console</p>
            <p>4. Configure your app with appropriate scopes and permissions</p>
        </div>

        <div class="step">
            <h3>Step 3: Get Your App Credentials</h3>
            
            <form id="tiktokConfigForm">
                <div class="form-group">
                    <label for="clientKey">Client Key:</label>
                    <input type="text" id="clientKey" name="clientKey" 
                           placeholder="Your TikTok app client key">
                </div>
                
                <div class="form-group">
                    <label for="clientSecret">Client Secret:</label>
                    <input type="password" id="clientSecret" name="clientSecret" 
                           placeholder="Your TikTok app client secret">
                </div>
                
                <div class="form-group">
                    <label for="accessToken">Access Token:</label>
                    <input type="password" id="accessToken" name="accessToken" 
                           placeholder="Your access token (obtained through OAuth)">
                </div>
                
                <div class="form-group">
                    <label for="refreshToken">Refresh Token:</label>
                    <input type="password" id="refreshToken" name="refreshToken" 
                           placeholder="Your refresh token">
                </div>
                
                <div class="form-group">
                    <label for="openId">Open ID:</label>
                    <input type="text" id="openId" name="openId" 
                           placeholder="Your TikTok Open ID">
                </div>
                
                <button type="button" onclick="saveConfig()">Save Configuration</button>
                <button type="button" onclick="testConnection()">Test Connection</button>
            </form>
            
            <div id="configResult"></div>
        </div>

        <div class="step">
            <h3>Step 4: OAuth Authorization Flow</h3>
            <p>To get access tokens, you need to implement OAuth 2.0 flow:</p>
            <div class="code">
                Authorization URL: https://www.tiktok.com/auth/authorize/<br>
                Token URL: https://open-api.tiktok.com/oauth/access_token/
            </div>
            <p>Required scopes for business features:</p>
            <ul>
                <li><strong>user.info.basic:</strong> Basic user information</li>
                <li><strong>video.list:</strong> Access to user's videos</li>
                <li><strong>video.upload:</strong> Upload videos</li>
                <li><strong>video.publish:</strong> Publish videos</li>
            </ul>
            <button type="button" onclick="initiateOAuth()">Start OAuth Flow</button>
            <div id="oauthResult"></div>
        </div>

        <div class="step">
            <h3>Step 5: Account Information</h3>
            <p>Once configured, get your TikTok account information:</p>
            <button type="button" onclick="getUserInfo()">Get User Info</button>
            <div id="userInfoResult"></div>
        </div>

        <div class="step">
            <h3>Step 6: Content Management</h3>
            <p>TikTok API allows you to manage your content:</p>
            <div class="form-group">
                <label for="videoPath">Video File Path:</label>
                <input type="text" id="videoPath" placeholder="/path/to/your/video.mp4">
            </div>
            <div class="form-group">
                <label for="videoDescription">Video Description:</label>
                <textarea id="videoDescription" rows="3" placeholder="Your video description with #hashtags"></textarea>
            </div>
            <button type="button" onclick="uploadVideo()">Upload Video</button>
            <button type="button" onclick="getVideoList()">Get My Videos</button>
            <div id="contentResult"></div>
        </div>

        <div class="step">
            <h3>Step 7: Engagement Features</h3>
            <p>Engage with your audience through comments and analytics:</p>
            <div class="form-group">
                <label for="videoId">Video ID:</label>
                <input type="text" id="videoId" placeholder="Video ID for comments/analytics">
            </div>
            <button type="button" onclick="getVideoComments()">Get Comments</button>
            <button type="button" onclick="getVideoAnalytics()">Get Analytics</button>
            <div id="engagementResult"></div>
        </div>

        <div class="step">
            <h3>Step 8: Enhanced Comment Interactions</h3>
            <p>Advanced comment management with sentiment analysis and priority replies:</p>

            <h4>Enhanced Comment Replies</h4>
            <div class="form-group">
                <label for="commentId">Comment ID:</label>
                <input type="text" id="commentId" placeholder="Comment ID to reply to">
            </div>
            <div class="form-group">
                <label for="replyText">Reply Text:</label>
                <textarea id="replyText" rows="2" placeholder="Your reply to the comment (max 150 chars)"></textarea>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="mentionUser" checked> Mention user in reply
                </label>
            </div>
            <button type="button" onclick="replyToCommentEnhanced()">Enhanced Reply</button>
            <div id="replyResult"></div>

            <h4>Comment Analysis</h4>
            <div class="form-group">
                <label for="analysisVideoId">Video ID for Comment Analysis:</label>
                <input type="text" id="analysisVideoId" placeholder="Video ID">
            </div>
            <button type="button" onclick="getCommentsWithSentiment()">Get Comments with Sentiment</button>
            <div id="sentimentResult"></div>
        </div>

        <div class="step">
            <h3>Step 9: Bulk Comment Messaging</h3>
            <div class="info">
                <strong>Send messages to multiple users via Unipile with TikTok comment interaction fallback</strong>
            </div>

            <div class="form-group">
                <label for="bulkRecipients">Recipient IDs (one per line):</label>
                <textarea id="bulkRecipients" rows="4" placeholder="user_id_1&#10;user_id_2&#10;user_id_3"></textarea>
            </div>
            <div class="form-group">
                <label for="bulkMessage">Bulk Message:</label>
                <textarea id="bulkMessage" rows="3" placeholder="Thanks for engaging with our TikTok content! 🎵"></textarea>
            </div>
            <div class="form-group">
                <label for="messageDelay">Delay between messages (seconds):</label>
                <input type="number" id="messageDelay" value="3" min="1" max="60">
            </div>
            <button type="button" onclick="sendBulkCommentMessages()">Send Bulk Messages</button>
            <div id="bulkResult"></div>
        </div>

        <div class="step">
            <h3>Step 10: Alternative Engagement Strategies</h3>
            <p>Since TikTok lacks direct messaging, consider these engagement methods:</p>
            <ul>
                <li><strong>Video Responses:</strong> Create video responses to user comments</li>
                <li><strong>Duets & Stitches:</strong> Engage with user content through duets</li>
                <li><strong>Live Streaming:</strong> Interact with followers in real-time</li>
                <li><strong>Comment Engagement:</strong> Actively respond to comments</li>
                <li><strong>Hashtag Challenges:</strong> Create branded hashtag challenges</li>
            </ul>
        </div>

        <div class="step">
            <h3>Step 11: Rate Limits & Best Practices</h3>
            <ul>
                <li><strong>Rate Limits:</strong> 100 requests per minute, 1000 per hour</li>
                <li><strong>Content Guidelines:</strong> Follow TikTok's community guidelines</li>
                <li><strong>Authentic Engagement:</strong> Focus on genuine interactions</li>
                <li><strong>Trending Content:</strong> Stay updated with TikTok trends</li>
                <li><strong>Analytics:</strong> Use analytics to optimize content strategy</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkUnipileStatus, 30000);

        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }

            fetch('/api/tiktok/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Unipile API connected successfully!</div>';
                    checkUnipileStatus(); // Refresh status
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Connection error: ' + error.message + '</div>';
            });
        }

        function checkUnipileStatus() {
            fetch('/api/tiktok/connection-status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const status = data.status;
                    let html = '<div class="info"><strong>📊 Connection Status:</strong><br>';

                    // Unipile status
                    if (status.unipile.available) {
                        if (status.unipile.connected) {
                            html += '🟢 Unipile: Connected (' + status.unipile.accounts.length + ' accounts)<br>';
                            status.unipile.accounts.forEach(account => {
                                html += '&nbsp;&nbsp;• ' + (account.username || account.name || account.account_id) + '<br>';
                            });
                        } else {
                            html += '🟡 Unipile: Available but not connected<br>';
                        }
                    } else {
                        html += '🔴 Unipile: Not available<br>';
                    }

                    // TikTok API status
                    if (status.tiktok_api.available) {
                        if (status.tiktok_api.connected) {
                            html += '🟢 TikTok API: Connected<br>';
                        } else {
                            html += '🟡 TikTok API: Configured but not connected<br>';
                        }
                    } else {
                        html += '🔴 TikTok API: Not configured<br>';
                    }

                    html += '</div>';
                    statusDiv.innerHTML = html;
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Status check error: ' + error.message + '</div>';
            });
        }

        function saveConfig() {
            const config = {
                unipile_api_key: document.getElementById('unipileApiKey').value,
                client_key: document.getElementById('clientKey').value,
                client_secret: document.getElementById('clientSecret').value,
                access_token: document.getElementById('accessToken').value,
                refresh_token: document.getElementById('refreshToken').value,
                open_id: document.getElementById('openId').value
            };
            
            fetch('/api/tiktok/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/tiktok/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }
        
        function initiateOAuth() {
            const clientKey = document.getElementById('clientKey').value;
            if (!clientKey) {
                document.getElementById('oauthResult').innerHTML = 
                    '<div class="error">❌ Please enter Client Key first</div>';
                return;
            }
            
            // Save the client key first
            const config = {
                client_key: clientKey,
                client_secret: document.getElementById('clientSecret').value
            };
            
            // First save the configuration
            fetch('/api/tiktok/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Now get the OAuth URL from our API
                    fetch('/api/tiktok/oauth-url')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.oauth_url) {
                            // Display the OAuth URL and open in a new window
                            document.getElementById('oauthResult').innerHTML = 
                                '<div class="info">🔗 <a href="' + data.oauth_url + '" target="_blank">Click here to authorize</a></div>';
                            window.open(data.oauth_url, '_blank');
                        } else {
                            document.getElementById('oauthResult').innerHTML = 
                                '<div class="error">❌ Failed to get OAuth URL: ' + (data.error || 'Unknown error') + '</div>';
                        }
                    })
                    .catch(error => {
                        document.getElementById('oauthResult').innerHTML = 
                            '<div class="error">❌ Error getting OAuth URL: ' + error.message + '</div>';
                    });
                } else {
                    document.getElementById('oauthResult').innerHTML = 
                        '<div class="error">❌ Error saving configuration: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('oauthResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function getUserInfo() {
            fetch('/api/tiktok/user-info')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('userInfoResult');
                if (data.success) {
                    const info = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>User Information:</strong><br>
                            Display Name: ${info.display_name}<br>
                            Open ID: ${info.open_id}<br>
                            Followers: ${info.follower_count}<br>
                            Following: ${info.following_count}<br>
                            Likes: ${info.likes_count}<br>
                            Videos: ${info.video_count}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get user info: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('userInfoResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function uploadVideo() {
            const videoPath = document.getElementById('videoPath').value;
            const description = document.getElementById('videoDescription').value;
            
            if (!videoPath) {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Please enter video file path</div>';
                return;
            }
            
            fetch('/api/tiktok/upload-video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_path: videoPath,
                    description: description
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Video uploaded successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to upload video: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoList() {
            fetch('/api/tiktok/videos')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success && data.data.videos) {
                    const videos = data.data.videos;
                    let html = '<div class="success"><strong>Your Videos:</strong><br>';
                    videos.slice(0, 5).forEach(video => {
                        html += `Title: ${video.title}, Views: ${video.view_count}, Likes: ${video.like_count}<br>`;
                    });
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get videos: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoComments() {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Please enter video ID</div>';
                return;
            }
            
            fetch(`/api/tiktok/comments/${videoId}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('engagementResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Comments retrieved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get comments: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoAnalytics() {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Please enter video ID</div>';
                return;
            }
            
            fetch(`/api/tiktok/analytics/${videoId}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('engagementResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Analytics retrieved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get analytics: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function replyToCommentEnhanced() {
            const videoId = document.getElementById('videoId').value;
            const commentId = document.getElementById('commentId').value;
            const replyText = document.getElementById('replyText').value;
            const mentionUser = document.getElementById('mentionUser').checked;

            if (!videoId || !commentId || !replyText) {
                document.getElementById('replyResult').innerHTML =
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }

            fetch('/api/tiktok/reply-comment-enhanced', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: videoId,
                    comment_id: commentId,
                    reply_text: replyText,
                    mention_user: mentionUser
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('replyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Enhanced reply posted successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to post reply: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('replyResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function getCommentsWithSentiment() {
            const videoId = document.getElementById('analysisVideoId').value;
            if (!videoId) {
                document.getElementById('sentimentResult').innerHTML =
                    '<div class="error">❌ Please enter video ID</div>';
                return;
            }

            fetch(`/api/tiktok/comments-sentiment/${videoId}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('sentimentResult');
                if (data.success && data.data.comments) {
                    const comments = data.data.comments;
                    let html = '<div class="success"><strong>Comments with Sentiment Analysis:</strong><br>';

                    comments.slice(0, 5).forEach(comment => {
                        const sentiment = comment.sentiment || 'neutral';
                        const priority = comment.reply_priority || 0;
                        const emoji = sentiment === 'positive' ? '😊' : sentiment === 'negative' ? '😞' : '😐';

                        html += `${emoji} <strong>Priority ${priority}:</strong> ${comment.text.substring(0, 50)}...<br>`;
                        html += `<small>Sentiment: ${sentiment} | Likes: ${comment.like_count || 0}</small><br><br>`;
                    });

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to analyze comments: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('sentimentResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendBulkCommentMessages() {
            const recipients = document.getElementById('bulkRecipients').value.split('\n').filter(id => id.trim());
            const message = document.getElementById('bulkMessage').value;
            const delay = parseFloat(document.getElementById('messageDelay').value) || 3;

            if (recipients.length === 0 || !message) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Please enter recipient IDs and message</div>';
                return;
            }

            // Show sending status
            document.getElementById('bulkResult').innerHTML =
                `<div class="info">📤 Sending bulk messages to ${recipients.length} recipients...</div>`;

            fetch('/api/tiktok/send-bulk-comment-messages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipients: recipients,
                    message: message,
                    delay: delay
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('bulkResult');
                if (data.success) {
                    const results = data.results || [];
                    const successful = results.filter(r => !r.result.error).length;
                    const failed = results.length - successful;

                    let html = `<div class="success">✅ Bulk messaging completed!<br>`;
                    html += `Successful: ${successful}, Failed: ${failed}<br><br>`;
                    html += '<strong>Details:</strong><br>';

                    results.forEach((result, index) => {
                        const status = result.result.error ? '❌' : '✅';
                        const method = result.method || 'unknown';
                        html += `${status} ${result.recipient_id} (${method})<br>`;
                    });

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Bulk messaging failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function replyToComment() {
            const videoId = document.getElementById('videoId').value;
            const commentId = document.getElementById('commentId').value;
            const replyText = document.getElementById('replyText').value;
            
            if (!videoId || !commentId || !replyText) {
                document.getElementById('replyResult').innerHTML = 
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }
            
            fetch('/api/tiktok/reply-comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: videoId,
                    comment_id: commentId,
                    reply_text: replyText
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('replyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Reply posted successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to post reply: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('replyResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>

// Add this function after the initiateOAuth function
function handleOAuthCallback() {
    // Get the code parameter from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    
    if (!code) {
        document.getElementById('oauthResult').innerHTML = 
            '<div class="error">❌ No authorization code found in the URL</div>';
        return;
    }
    
    // Call our API endpoint to handle the callback
    fetch(`/api/tiktok/callback?code=${encodeURIComponent(code)}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the UI with success message
            document.getElementById('oauthResult').innerHTML = 
                '<div class="success">✅ Authentication successful! Access token received.</div>';
            
            // Update the form fields with the received tokens
            if (data.data) {
                if (data.data.access_token) {
                    document.getElementById('accessToken').value = data.data.access_token;
                }
                if (data.data.refresh_token) {
                    document.getElementById('refreshToken').value = data.data.refresh_token;
                }
                if (data.data.open_id) {
                    document.getElementById('openId').value = data.data.open_id;
                }
            }
            
            // Check connection status
            checkConnectionStatus();
        } else {
            document.getElementById('oauthResult').innerHTML = 
                '<div class="error">❌ Authentication failed: ' + (data.error || 'Unknown error') + '</div>';
        }
    })
    .catch(error => {
        document.getElementById('oauthResult').innerHTML = 
            '<div class="error">❌ Error processing callback: ' + error.message + '</div>';
    });
}

// Add this code at the end of the script section to check for callback on page load
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're returning from OAuth redirect
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('code')) {
        handleOAuthCallback();
    }
});


        </div>

        <div class="step">
            <h3>Step 7: Engagement Features</h3>
            <p>Engage with your audience through comments and analytics:</p>
            <div class="form-group">
                <label for="videoId">Video ID:</label>
                <input type="text" id="videoId" placeholder="Video ID for comments/analytics">
            </div>
            <button type="button" onclick="getVideoComments()">Get Comments</button>
            <button type="button" onclick="getVideoAnalytics()">Get Analytics</button>
            <div id="engagementResult"></div>
        </div>

        <div class="step">
            <h3>Step 8: Enhanced Comment Interactions</h3>
            <p>Advanced comment management with sentiment analysis and priority replies:</p>

            <h4>Enhanced Comment Replies</h4>
            <div class="form-group">
                <label for="commentId">Comment ID:</label>
                <input type="text" id="commentId" placeholder="Comment ID to reply to">
            </div>
            <div class="form-group">
                <label for="replyText">Reply Text:</label>
                <textarea id="replyText" rows="2" placeholder="Your reply to the comment (max 150 chars)"></textarea>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="mentionUser" checked> Mention user in reply
                </label>
            </div>
            <button type="button" onclick="replyToCommentEnhanced()">Enhanced Reply</button>
            <div id="replyResult"></div>

            <h4>Comment Analysis</h4>
            <div class="form-group">
                <label for="analysisVideoId">Video ID for Comment Analysis:</label>
                <input type="text" id="analysisVideoId" placeholder="Video ID">
            </div>
            <button type="button" onclick="getCommentsWithSentiment()">Get Comments with Sentiment</button>
            <div id="sentimentResult"></div>
        </div>

        <div class="step">
            <h3>Step 9: Bulk Comment Messaging</h3>
            <div class="info">
                <strong>Send messages to multiple users via Unipile with TikTok comment interaction fallback</strong>
            </div>

            <div class="form-group">
                <label for="bulkRecipients">Recipient IDs (one per line):</label>
                <textarea id="bulkRecipients" rows="4" placeholder="user_id_1&#10;user_id_2&#10;user_id_3"></textarea>
            </div>
            <div class="form-group">
                <label for="bulkMessage">Bulk Message:</label>
                <textarea id="bulkMessage" rows="3" placeholder="Thanks for engaging with our TikTok content! 🎵"></textarea>
            </div>
            <div class="form-group">
                <label for="messageDelay">Delay between messages (seconds):</label>
                <input type="number" id="messageDelay" value="3" min="1" max="60">
            </div>
            <button type="button" onclick="sendBulkCommentMessages()">Send Bulk Messages</button>
            <div id="bulkResult"></div>
        </div>

        <div class="step">
            <h3>Step 10: Alternative Engagement Strategies</h3>
            <p>Since TikTok lacks direct messaging, consider these engagement methods:</p>
            <ul>
                <li><strong>Video Responses:</strong> Create video responses to user comments</li>
                <li><strong>Duets & Stitches:</strong> Engage with user content through duets</li>
                <li><strong>Live Streaming:</strong> Interact with followers in real-time</li>
                <li><strong>Comment Engagement:</strong> Actively respond to comments</li>
                <li><strong>Hashtag Challenges:</strong> Create branded hashtag challenges</li>
            </ul>
        </div>

        <div class="step">
            <h3>Step 11: Rate Limits & Best Practices</h3>
            <ul>
                <li><strong>Rate Limits:</strong> 100 requests per minute, 1000 per hour</li>
                <li><strong>Content Guidelines:</strong> Follow TikTok's community guidelines</li>
                <li><strong>Authentic Engagement:</strong> Focus on genuine interactions</li>
                <li><strong>Trending Content:</strong> Stay updated with TikTok trends</li>
                <li><strong>Analytics:</strong> Use analytics to optimize content strategy</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkUnipileStatus, 30000);

        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }

            fetch('/api/tiktok/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Unipile API connected successfully!</div>';
                    checkUnipileStatus(); // Refresh status
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Connection error: ' + error.message + '</div>';
            });
        }

        function checkUnipileStatus() {
            fetch('/api/tiktok/connection-status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const status = data.status;
                    let html = '<div class="info"><strong>📊 Connection Status:</strong><br>';

                    // Unipile status
                    if (status.unipile.available) {
                        if (status.unipile.connected) {
                            html += '🟢 Unipile: Connected (' + status.unipile.accounts.length + ' accounts)<br>';
                            status.unipile.accounts.forEach(account => {
                                html += '&nbsp;&nbsp;• ' + (account.username || account.name || account.account_id) + '<br>';
                            });
                        } else {
                            html += '🟡 Unipile: Available but not connected<br>';
                        }
                    } else {
                        html += '🔴 Unipile: Not available<br>';
                    }

                    // TikTok API status
                    if (status.tiktok_api.available) {
                        if (status.tiktok_api.connected) {
                            html += '🟢 TikTok API: Connected<br>';
                        } else {
                            html += '🟡 TikTok API: Configured but not connected<br>';
                        }
                    } else {
                        html += '🔴 TikTok API: Not configured<br>';
                    }

                    html += '</div>';
                    statusDiv.innerHTML = html;
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Status check error: ' + error.message + '</div>';
            });
        }

        function saveConfig() {
            const config = {
                unipile_api_key: document.getElementById('unipileApiKey').value,
                client_key: document.getElementById('clientKey').value,
                client_secret: document.getElementById('clientSecret').value,
                access_token: document.getElementById('accessToken').value,
                refresh_token: document.getElementById('refreshToken').value,
                open_id: document.getElementById('openId').value
            };
            
            fetch('/api/tiktok/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/tiktok/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }
        
        function initiateOAuth() {
            const clientKey = document.getElementById('clientKey').value;
            if (!clientKey) {
                document.getElementById('oauthResult').innerHTML = 
                    '<div class="error">❌ Please enter Client Key first</div>';
                return;
            }
            
            // Save the client key first
            const config = {
                client_key: clientKey,
                client_secret: document.getElementById('clientSecret').value
            };
            
            // First save the configuration
            fetch('/api/tiktok/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Now get the OAuth URL from our API
                    fetch('/api/tiktok/oauth-url')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.oauth_url) {
                            // Display the OAuth URL and open in a new window
                            document.getElementById('oauthResult').innerHTML = 
                                '<div class="info">🔗 <a href="' + data.oauth_url + '" target="_blank">Click here to authorize</a></div>';
                            window.open(data.oauth_url, '_blank');
                        } else {
                            document.getElementById('oauthResult').innerHTML = 
                                '<div class="error">❌ Failed to get OAuth URL: ' + (data.error || 'Unknown error') + '</div>';
                        }
                    })
                    .catch(error => {
                        document.getElementById('oauthResult').innerHTML = 
                            '<div class="error">❌ Error getting OAuth URL: ' + error.message + '</div>';
                    });
                } else {
                    document.getElementById('oauthResult').innerHTML = 
                        '<div class="error">❌ Error saving configuration: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('oauthResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function getUserInfo() {
            fetch('/api/tiktok/user-info')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('userInfoResult');
                if (data.success) {
                    const info = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>User Information:</strong><br>
                            Display Name: ${info.display_name}<br>
                            Open ID: ${info.open_id}<br>
                            Followers: ${info.follower_count}<br>
                            Following: ${info.following_count}<br>
                            Likes: ${info.likes_count}<br>
                            Videos: ${info.video_count}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get user info: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('userInfoResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function uploadVideo() {
            const videoPath = document.getElementById('videoPath').value;
            const description = document.getElementById('videoDescription').value;
            
            if (!videoPath) {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Please enter video file path</div>';
                return;
            }
            
            fetch('/api/tiktok/upload-video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_path: videoPath,
                    description: description
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Video uploaded successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to upload video: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoList() {
            fetch('/api/tiktok/videos')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success && data.data.videos) {
                    const videos = data.data.videos;
                    let html = '<div class="success"><strong>Your Videos:</strong><br>';
                    videos.slice(0, 5).forEach(video => {
                        html += `Title: ${video.title}, Views: ${video.view_count}, Likes: ${video.like_count}<br>`;
                    });
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get videos: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoComments() {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Please enter video ID</div>';
                return;
            }
            
            fetch(`/api/tiktok/comments/${videoId}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('engagementResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Comments retrieved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get comments: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoAnalytics() {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Please enter video ID</div>';
                return;
            }
            
            fetch(`/api/tiktok/analytics/${videoId}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('engagementResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Analytics retrieved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get analytics: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function replyToCommentEnhanced() {
            const videoId = document.getElementById('videoId').value;
            const commentId = document.getElementById('commentId').value;
            const replyText = document.getElementById('replyText').value;
            const mentionUser = document.getElementById('mentionUser').checked;

            if (!videoId || !commentId || !replyText) {
                document.getElementById('replyResult').innerHTML =
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }

            fetch('/api/tiktok/reply-comment-enhanced', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: videoId,
                    comment_id: commentId,
                    reply_text: replyText,
                    mention_user: mentionUser
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('replyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Enhanced reply posted successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to post reply: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('replyResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function getCommentsWithSentiment() {
            const videoId = document.getElementById('analysisVideoId').value;
            if (!videoId) {
                document.getElementById('sentimentResult').innerHTML =
                    '<div class="error">❌ Please enter video ID</div>';
                return;
            }

            fetch(`/api/tiktok/comments-sentiment/${videoId}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('sentimentResult');
                if (data.success && data.data.comments) {
                    const comments = data.data.comments;
                    let html = '<div class="success"><strong>Comments with Sentiment Analysis:</strong><br>';

                    comments.slice(0, 5).forEach(comment => {
                        const sentiment = comment.sentiment || 'neutral';
                        const priority = comment.reply_priority || 0;
                        const emoji = sentiment === 'positive' ? '😊' : sentiment === 'negative' ? '😞' : '😐';

                        html += `${emoji} <strong>Priority ${priority}:</strong> ${comment.text.substring(0, 50)}...<br>`;
                        html += `<small>Sentiment: ${sentiment} | Likes: ${comment.like_count || 0}</small><br><br>`;
                    });

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to analyze comments: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('sentimentResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendBulkCommentMessages() {
            const recipients = document.getElementById('bulkRecipients').value.split('\n').filter(id => id.trim());
            const message = document.getElementById('bulkMessage').value;
            const delay = parseFloat(document.getElementById('messageDelay').value) || 3;

            if (recipients.length === 0 || !message) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Please enter recipient IDs and message</div>';
                return;
            }

            // Show sending status
            document.getElementById('bulkResult').innerHTML =
                `<div class="info">📤 Sending bulk messages to ${recipients.length} recipients...</div>`;

            fetch('/api/tiktok/send-bulk-comment-messages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipients: recipients,
                    message: message,
                    delay: delay
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('bulkResult');
                if (data.success) {
                    const results = data.results || [];
                    const successful = results.filter(r => !r.result.error).length;
                    const failed = results.length - successful;

                    let html = `<div class="success">✅ Bulk messaging completed!<br>`;
                    html += `Successful: ${successful}, Failed: ${failed}<br><br>`;
                    html += '<strong>Details:</strong><br>';

                    results.forEach((result, index) => {
                        const status = result.result.error ? '❌' : '✅';
                        const method = result.method || 'unknown';
                        html += `${status} ${result.recipient_id} (${method})<br>`;
                    });

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Bulk messaging failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function replyToComment() {
            const videoId = document.getElementById('videoId').value;
            const commentId = document.getElementById('commentId').value;
            const replyText = document.getElementById('replyText').value;
            
            if (!videoId || !commentId || !replyText) {
                document.getElementById('replyResult').innerHTML = 
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }
            
            fetch('/api/tiktok/reply-comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: videoId,
                    comment_id: commentId,
                    reply_text: replyText
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('replyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Reply posted successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to post reply: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('replyResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
