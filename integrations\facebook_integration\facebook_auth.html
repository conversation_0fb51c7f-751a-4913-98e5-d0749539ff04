<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Messenger Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1877f2;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #1877f2;
            background-color: #f9f9f9;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #1877f2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #166fe5;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #1877f2;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📘 Facebook Messenger Setup</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> Facebook Messenger API requires a Facebook Page and approved app.
            Make sure you have admin access to a Facebook Page and have completed business verification if required.
        </div>

        <!-- Connection Status -->
        <div class="step">
            <h3>📊 Current Connection Status</h3>
            <button type="button" onclick="checkConnectionStatus()" style="margin-bottom: 15px;">🔄 Check Status</button>
            <div id="connectionStatus">
                <div class="info">Click "Check Status" to see your current Facebook connection status.</div>
            </div>
        </div>

        <div class="step">
            <h3>Step 1: Create Facebook App</h3>
            <p>1. Go to <a href="https://developers.facebook.com/" target="_blank">Facebook Developers</a></p>
            <p>2. Click "Create App" and select "Business" type</p>
            <p>3. Add "Messenger" product to your app</p>
            <p>4. Complete app review process if needed for production use</p>
        </div>

        <div class="step">
            <h3>Step 2: Connect Your Facebook Page</h3>
            <p>1. In your app dashboard, go to Messenger > Settings</p>
            <p>2. In the "Access Tokens" section, select your Facebook Page</p>
            <p>3. Copy the Page Access Token</p>
            <p>4. Note your Page ID (found in your Facebook Page settings)</p>
        </div>

        <div class="step">
            <h3>Step 3: Authentication Options</h3>

            <!-- OAuth Authentication (Recommended) -->
            <div style="margin-bottom: 30px; padding: 20px; border: 2px solid #1877f2; border-radius: 10px; background: #f8f9ff;">
                <h4 style="color: #1877f2; margin-top: 0;">🔐 Option 1: OAuth Authentication (Recommended)</h4>
                <p>Automatically connect your Facebook Page with secure OAuth flow:</p>
                <button type="button" onclick="startOAuthFlow()" style="background: linear-gradient(45deg, #1877f2, #42a5f5); font-size: 16px; padding: 15px 30px;">
                    🔗 Connect with Facebook
                </button>
                <div id="oauthResult" style="margin-top: 15px;"></div>
                <div id="oauthStatus" style="margin-top: 10px;"></div>
            </div>

            <!-- Manual Configuration -->
            <div style="padding: 20px; border: 1px solid #ddd; border-radius: 10px; background: #f9f9f9;">
                <h4 style="margin-top: 0;">⚙️ Option 2: Manual Configuration</h4>
                <p>Enter your Facebook credentials manually:</p>

                <form id="facebookConfigForm">
                <div class="form-group">
                    <label for="pageAccessToken">Page Access Token:</label>
                    <input type="password" id="pageAccessToken" name="pageAccessToken" 
                           placeholder="Your Facebook Page Access Token">
                </div>
                
                <div class="form-group">
                    <label for="appSecret">App Secret:</label>
                    <input type="password" id="appSecret" name="appSecret" 
                           placeholder="Your Facebook App Secret">
                </div>
                
                <div class="form-group">
                    <label for="pageId">Page ID:</label>
                    <input type="text" id="pageId" name="pageId" 
                           placeholder="Your Facebook Page ID">
                </div>

                <button type="button" onclick="saveConfig()">Save Configuration</button>
                <button type="button" onclick="testConnection()">Test Connection</button>
            </form>

            <div id="configResult"></div>
            </div>
        </div>

        <div class="step">
            <h3>Step 4: Page Setup</h3>
            <p>Configure your Facebook Page for messaging:</p>
            <div class="form-group">
                <label for="greetingText">Greeting Text:</label>
                <textarea id="greetingText" rows="2" placeholder="Welcome! How can we help you today?"></textarea>
            </div>
            <button type="button" onclick="setGreeting()">Set Greeting</button>
            <button type="button" onclick="setGetStarted()">Set Get Started Button</button>
            <div id="pageSetupResult"></div>
        </div>

        <div class="step">
            <h3>Step 5: Test Your Setup</h3>
            <div class="info">
                <strong>Note:</strong> To test messaging, you need a user's Page-Scoped ID (PSID). 
                Users get a PSID when they first interact with your page.
            </div>
            
            <div class="form-group">
                <label for="testUserId">User PSID (Page-Scoped ID):</label>
                <input type="text" id="testUserId" placeholder="User's Page-Scoped ID">
            </div>
            <div class="form-group">
                <label for="testMessage">Test Message:</label>
                <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from Facebook Messenger."></textarea>
            </div>
            <button type="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="testResult"></div>
        </div>

        <div class="step">
            <h3>Step 6: Getting User PSIDs</h3>
            <p>To send messages to users, you need their Page-Scoped IDs (PSIDs):</p>
            <ul>
                <li><strong>Organic:</strong> Users message your page first, webhook receives their PSID</li>
                <li><strong>Opt-in:</strong> Use Messenger plugins on your website</li>
                <li><strong>Customer Chat:</strong> Add customer chat plugin to your website</li>
                <li><strong>Send-to-Messenger:</strong> Use Send-to-Messenger button</li>
            </ul>
            <p><strong>Important:</strong> You can only message users who have initiated contact or opted in.</p>
        </div>

        <div class="step">
            <h3>Step 7: Message Templates</h3>
            <p>For promotional messages outside the 24-hour window, you need approved message templates:</p>
            <ul>
                <li>Go to your Facebook App > Messenger > Message Templates</li>
                <li>Create and submit templates for approval</li>
                <li>Use approved templates for promotional messaging</li>
            </ul>
        </div>
    </div>

    <script>
        // Connection Status Functions
        function checkConnectionStatus() {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.innerHTML = '<div class="info">🔄 Checking connection status...</div>';

            fetch('/api/facebook/status')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.connected) {
                    statusDiv.innerHTML = `
                        <div class="success">
                            ✅ Facebook Messenger Connected!<br>
                            <strong>Page Name:</strong> ${data.page_name || 'N/A'}<br>
                            <strong>Page ID:</strong> ${data.page_id || 'N/A'}<br>
                            <strong>Status:</strong> Ready to send messages<br>
                            <small>Last checked: ${new Date().toLocaleString()}</small>
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="warning">
                            ⚠️ Facebook Messenger Not Connected<br>
                            Please use one of the authentication options below to connect your Facebook Page.
                        </div>
                    `;
                }
            })
            .catch(error => {
                statusDiv.innerHTML = '<div class="error">❌ Error checking status: ' + error.message + '</div>';
            });
        }

        // Auto-check status on page load
        window.onload = function() {
            checkConnectionStatus();
        };

        // OAuth Authentication Functions
        function startOAuthFlow() {
            const resultDiv = document.getElementById('oauthResult');
            const statusDiv = document.getElementById('oauthStatus');

            resultDiv.innerHTML = '<div class="info">🔄 Starting OAuth authentication...</div>';

            // Step 1: Get OAuth URL from backend
            fetch('/api/facebook/oauth/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.auth_url) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ OAuth URL generated!<br>
                            <strong>Click the button below to authenticate:</strong><br><br>
                            <a href="${data.auth_url}" target="_blank" style="background: #1877f2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                                🔗 Authenticate with Facebook
                            </a>
                        </div>
                    `;

                    // Start polling for completion
                    pollOAuthStatus(data.state);
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to start OAuth: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function pollOAuthStatus(state) {
            const statusDiv = document.getElementById('oauthStatus');
            statusDiv.innerHTML = '<div class="info">⏳ Waiting for authentication completion...</div>';

            const pollInterval = setInterval(() => {
                fetch(`/api/facebook/oauth/status?state=${state}`)
                .then(response => response.json())
                .then(data => {
                    if (data.completed) {
                        clearInterval(pollInterval);
                        if (data.success) {
                            statusDiv.innerHTML = `
                                <div class="success">
                                    🎉 Authentication successful!<br>
                                    <strong>Page:</strong> ${data.page_name || 'Connected'}<br>
                                    <strong>Page ID:</strong> ${data.page_id || 'N/A'}<br>
                                    You can now use Facebook Messenger integration!
                                </div>
                            `;

                            // Auto-refresh page setup section
                            setTimeout(() => {
                                location.reload();
                            }, 3000);
                        } else {
                            statusDiv.innerHTML = '<div class="error">❌ Authentication failed: ' + (data.error || 'Unknown error') + '</div>';
                        }
                    }
                })
                .catch(error => {
                    console.error('Polling error:', error);
                });
            }, 2000); // Poll every 2 seconds

            // Stop polling after 5 minutes
            setTimeout(() => {
                clearInterval(pollInterval);
                if (statusDiv.innerHTML.includes('Waiting for')) {
                    statusDiv.innerHTML = '<div class="warning">⏰ Authentication timeout. Please try again.</div>';
                }
            }, 300000);
        }

        // Manual Configuration Functions
        function saveConfig() {
            const config = {
                page_access_token: document.getElementById('pageAccessToken').value,
                app_secret: document.getElementById('appSecret').value,
                page_id: document.getElementById('pageId').value
            };
            
            fetch('/api/facebook/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/facebook/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }

        function setGreeting() {
            const greetingText = document.getElementById('greetingText').value;
            if (!greetingText) {
                document.getElementById('pageSetupResult').innerHTML = 
                    '<div class="error">❌ Please enter greeting text</div>';
                return;
            }
            
            fetch('/api/facebook/greeting', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ greeting: greetingText })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('pageSetupResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Greeting text set successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to set greeting: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('pageSetupResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function setGetStarted() {
            fetch('/api/facebook/get-started', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('pageSetupResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Get Started button configured!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to set Get Started button: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('pageSetupResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendTestMessage() {
            const userId = document.getElementById('testUserId').value;
            const message = document.getElementById('testMessage').value;
            
            if (!userId || !message) {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Please enter both user PSID and message</div>';
                return;
            }
            
            fetch('/api/facebook/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: userId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Test message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send test message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
