#!/usr/bin/env python3
"""
Simple server runner for the Telegram integration
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("🚀 Starting Telegram Integration Server...")
        print("📍 Server will be available at: http://localhost:8000")
        print("📖 API documentation: http://localhost:8000/docs")
        print("📱 Telegram setup: http://localhost:8000/telegram/telegram_auth.html")
        print("-" * 60)
        
        # Import and run
        import uvicorn
        from integrations.api_endpoints import app
        
        print("✅ Imports successful, starting server...")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you have installed the requirements:")
        print("   pip install fastapi uvicorn")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
