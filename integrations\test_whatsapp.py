#!/usr/bin/env python3
"""
WhatsApp Integration Test Script
Tests WhatsApp functionality including authentication and messaging
"""

import sys
import os
import json
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_whatsapp_integration():
    """Test WhatsApp integration functionality"""
    print("🔧 Testing WhatsApp Integration")
    print("=" * 50)
    
    try:
        # Import WhatsApp messaging
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        print("✅ WhatsApp API imported successfully")
        
        # Initialize WhatsApp client
        whatsapp = WhatsAppMessaging()
        print("✅ WhatsApp client initialized")
        
        # Test 1: Check if configured
        is_configured = whatsapp.is_configured()
        print(f"📋 Configuration status: {'✅ Configured' if is_configured else '❌ Not configured'}")
        
        # Test 2: Check connection status
        print("\n📡 Checking connection status...")
        status = whatsapp.check_connection_status()
        print(f"   Available: {status.get('available', False)}")
        print(f"   Connected: {status.get('connected', False)}")
        print(f"   Accounts: {len(status.get('accounts', []))}")
        
        if status.get('accounts'):
            print("   Connected accounts:")
            for account in status['accounts']:
                print(f"     • {account.get('username', account.get('name', account.get('id', 'Unknown')))}")
        
        # Test 3: Test connection
        print("\n🔍 Testing API connection...")
        test_result = whatsapp.test_connection()
        print(f"   API Available: {test_result.get('available', False)}")
        print(f"   API Connected: {test_result.get('connected', False)}")
        
        # Test 4: Authentication (QR code generation)
        print("\n🔐 Testing authentication...")
        auth_result = whatsapp.authenticate_account()
        
        if auth_result.get('success'):
            if auth_result.get('qr_required'):
                print("✅ QR code generated successfully!")
                print("📱 Please scan the QR code with your WhatsApp mobile app")
                print("   QR code data is available for display")
                
                # Save QR code data to file for manual testing
                if auth_result.get('qr_code'):
                    qr_file = 'whatsapp_qr_code.html'
                    with open(qr_file, 'w', encoding='utf-8') as f:
                        f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <title>WhatsApp QR Code</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; padding: 20px; }}
        .qr-container {{ margin: 20px auto; max-width: 400px; }}
        img {{ max-width: 100%; border: 2px solid #25D366; border-radius: 10px; }}
    </style>
</head>
<body>
    <h1>🔐 WhatsApp Authentication</h1>
    <div class="qr-container">
        <h2>📱 Scan this QR code with WhatsApp</h2>
        <img src="{auth_result['qr_code']}" alt="WhatsApp QR Code">
        <p><strong>Instructions:</strong></p>
        <ol style="text-align: left;">
            <li>Open WhatsApp on your phone</li>
            <li>Go to Settings → Linked Devices</li>
            <li>Tap "Link a Device"</li>
            <li>Scan this QR code</li>
        </ol>
        <p><em>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</em></p>
    </div>
</body>
</html>
                        """)
                    print(f"💾 QR code saved to: {qr_file}")
                    print(f"🌐 Open file://{os.path.abspath(qr_file)} in your browser")
                    
            else:
                print("✅ WhatsApp account already connected!")
        else:
            print(f"❌ Authentication failed: {auth_result.get('error', 'Unknown error')}")
        
        # Test 5: Message sending (only if connected)
        if status.get('connected'):
            print("\n📤 Testing message sending...")
            print("⚠️  Message sending test skipped (requires valid phone number)")
            print("   To test messaging, use:")
            print("   result = whatsapp.send_message('+**********', 'Test message')")
        else:
            print("\n📤 Message sending test skipped (not connected)")
            print("   Connect your WhatsApp account first using the QR code")
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Summary:")
        print(f"   ✅ Import: Success")
        print(f"   ✅ Initialization: Success")
        print(f"   {'✅' if is_configured else '❌'} Configuration: {'Complete' if is_configured else 'Missing'}")
        print(f"   {'✅' if status.get('available') else '❌'} API Available: {status.get('available', False)}")
        print(f"   {'✅' if status.get('connected') else '❌'} Connected: {status.get('connected', False)}")
        print(f"   {'✅' if auth_result.get('success') else '❌'} Authentication: {'Working' if auth_result.get('success') else 'Failed'}")
        
        if status.get('connected'):
            print("\n🎉 WhatsApp integration is fully working!")
            print("   You can now send messages using the API")
        elif auth_result.get('success') and auth_result.get('qr_required'):
            print("\n⏳ WhatsApp integration is ready for connection!")
            print("   Scan the QR code to complete setup")
        else:
            print("\n⚠️  WhatsApp integration needs attention")
            print("   Check the error messages above")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install requests qrcode pillow")
        return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_api_endpoints():
    """Test API endpoints if server is running"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    try:
        import requests
        
        base_url = "http://localhost:8000"
        
        # Test WhatsApp status endpoint
        try:
            response = requests.get(f"{base_url}/api/whatsapp/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print("✅ WhatsApp status endpoint working")
                print(f"   Connected: {data.get('status', {}).get('connected', False)}")
            else:
                print(f"⚠️  WhatsApp status endpoint returned {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Server not running on localhost:8000")
            print("💡 Start the server with: python run_server.py")
        except Exception as e:
            print(f"❌ API test error: {e}")
            
    except ImportError:
        print("❌ Requests library not available")
        print("💡 Install with: pip install requests")

if __name__ == "__main__":
    print("🚀 WhatsApp Integration Test Suite")
    print("=" * 60)
    
    # Test core functionality
    success = test_whatsapp_integration()
    
    # Test API endpoints
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 WhatsApp integration test completed!")
        print("📖 Check the output above for detailed results")
    else:
        print("❌ WhatsApp integration test failed!")
        print("🔧 Please fix the issues and try again")
