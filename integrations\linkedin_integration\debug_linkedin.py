#!/usr/bin/env python3
"""
LinkedIn Integration Debug Tool
Helps debug LinkedIn connection request issues
"""

import sys
import os
import json
from datetime import datetime

# Add the parent directory to the path so we can import the LinkedIn API
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from linkedin_integration.linkedin_api import LinkedInMessaging, validate_linkedin_id

def print_separator(title=""):
    """Print a visual separator"""
    print("\n" + "="*60)
    if title:
        print(f" {title}")
        print("="*60)
    print()

def debug_identifier(identifier: str):
    """Debug a specific LinkedIn identifier"""
    print_separator(f"DEBUGGING LINKEDIN IDENTIFIER: {identifier}")
    
    # Initialize LinkedIn messaging
    try:
        linkedin = LinkedInMessaging()
        print("✅ LinkedIn messaging client initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize LinkedIn client: {e}")
        return
    
    # Basic validation
    print("\n📋 BASIC VALIDATION:")
    is_valid = validate_linkedin_id(identifier)
    identifier_type = "provider_id" if identifier.startswith('ACoA') else "public_identifier"
    
    print(f"   Identifier: {identifier}")
    print(f"   Type: {identifier_type}")
    print(f"   Length: {len(identifier)}")
    print(f"   Valid format: {'✅' if is_valid else '❌'} {is_valid}")
    
    # Get account info
    print("\n🔗 ACCOUNT INFORMATION:")
    account_id = linkedin._get_linkedin_account_id()
    if account_id:
        print(f"   Your LinkedIn Account ID: {account_id}")
        print(f"   Self-connection check: {'❌ CANNOT CONNECT TO YOURSELF' if identifier == account_id else '✅ OK'}")
    else:
        print("   ❌ No LinkedIn account ID found - check Unipile connection")
    
    # Unipile client status
    print("\n🔌 UNIPILE CLIENT STATUS:")
    if linkedin.unipile_client:
        print("   ✅ Unipile client available")
        
        # Test accounts endpoint
        try:
            accounts = linkedin.unipile_client.get_accounts()
            if "error" not in accounts:
                linkedin_accounts = [acc for acc in accounts.get("items", []) if acc.get("type") == "LINKEDIN"]
                print(f"   ✅ Connected LinkedIn accounts: {len(linkedin_accounts)}")
                for acc in linkedin_accounts:
                    print(f"      - Account ID: {acc.get('id')}")
                    print(f"      - Name: {acc.get('name', 'N/A')}")
            else:
                print(f"   ❌ Failed to get accounts: {accounts['error']}")
        except Exception as e:
            print(f"   ❌ Error getting accounts: {e}")
    else:
        print("   ❌ Unipile client not available")
    
    # LinkedIn API fallback status
    print("\n🔄 LINKEDIN API FALLBACK:")
    if linkedin.access_token and linkedin.access_token != "YOUR_LINKEDIN_ACCESS_TOKEN":
        print("   ✅ LinkedIn API credentials configured")
    else:
        print("   ❌ LinkedIn API credentials not configured (fallback disabled)")
    
    # Profile lookup test
    if linkedin.unipile_client and account_id:
        print("\n🔍 PROFILE LOOKUP TEST:")
        try:
            profile_result = linkedin.unipile_client.get_linkedin_profile(account_id, identifier)
            if "error" not in profile_result:
                print("   ✅ Profile lookup successful")
                print(f"   Profile data keys: {list(profile_result.keys())}")

                # Show important profile details
                provider_id = profile_result.get("provider_id")
                public_identifier = profile_result.get("public_identifier")
                first_name = profile_result.get("first_name")
                last_name = profile_result.get("last_name")
                headline = profile_result.get("headline")

                print(f"   📋 Profile Details:")
                print(f"      Name: {first_name} {last_name}")
                print(f"      Headline: {headline}")
                print(f"      Public Identifier: {public_identifier}")
                print(f"      Provider ID: {provider_id}")

                if provider_id and provider_id != identifier:
                    print(f"   🔄 Conversion: {identifier} → {provider_id}")
                    print("   💡 Connection requests will use the provider_id")
            else:
                print(f"   ❌ Profile lookup failed: {profile_result['error']}")
                if "suggestions" in profile_result:
                    print("   💡 Suggestions:")
                    for suggestion in profile_result["suggestions"]:
                        print(f"      - {suggestion}")
        except Exception as e:
            print(f"   ❌ Profile lookup error: {e}")
    
    # Connection request test (dry run)
    print("\n🧪 CONNECTION REQUEST TEST (DRY RUN):")
    if identifier == account_id:
        print("   ❌ SKIPPED - Cannot test connection to yourself")
    elif not linkedin.unipile_client:
        print("   ❌ SKIPPED - Unipile client not available")
    elif not account_id:
        print("   ❌ SKIPPED - No account ID available")
    else:
        print("   ℹ️  This would attempt a connection request to:")
        print(f"      From: {account_id}")
        print(f"      To: {identifier}")
        print("   ⚠️  Run with --test-connection to actually attempt the request")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    recommendations = []
    
    if not is_valid:
        recommendations.append("Fix the identifier format")
    if identifier == account_id:
        recommendations.append("Use a different person's LinkedIn identifier")
    if not linkedin.unipile_client:
        recommendations.append("Check Unipile API configuration")
    if not account_id:
        recommendations.append("Connect a LinkedIn account to Unipile")
    if not (linkedin.access_token and linkedin.access_token != "YOUR_LINKEDIN_ACCESS_TOKEN"):
        recommendations.append("Configure LinkedIn API credentials for fallback")
    
    if not recommendations:
        recommendations.append("Identifier looks good - try the connection request")
    
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. {rec}")
    
    print_separator()

def test_connection_request(identifier: str, message: str = "Hello! I'd like to connect with you on LinkedIn."):
    """Actually test a connection request"""
    print_separator(f"TESTING CONNECTION REQUEST TO: {identifier}")

    try:
        linkedin = LinkedInMessaging()
        result = linkedin.send_connection_message(identifier, message)

        if result.get("success"):
            print("✅ CONNECTION REQUEST SUCCESSFUL!")
            print(f"   Method used: {result.get('method', 'unknown')}")
            print(f"   Timestamp: {result.get('timestamp', 'N/A')}")
        else:
            print("❌ CONNECTION REQUEST FAILED")
            print(f"   Error: {result.get('error', 'Unknown error')}")

            if "suggestions" in result:
                print("\n💡 Suggestions:")
                for suggestion in result["suggestions"]:
                    print(f"   - {suggestion}")

            if "solutions" in result:
                print("\n🔧 Solutions:")
                for solution in result["solutions"]:
                    print(f"   - {solution}")

    except Exception as e:
        print(f"❌ Exception during connection request: {e}")

    print_separator()

def test_inmail(identifier: str, subject: str = "Professional Opportunity", message: str = "Hello! I hope this message finds you well. I'd like to discuss a potential opportunity with you."):
    """Actually test an InMail"""
    print_separator(f"TESTING INMAIL TO: {identifier}")

    try:
        linkedin = LinkedInMessaging()
        result = linkedin.send_inmail(identifier, subject, message)

        if result.get("success"):
            print("✅ INMAIL SUCCESSFUL!")
            print(f"   Method used: {result.get('method', 'unknown')}")
            print(f"   Subject: {result.get('subject', 'N/A')}")
            print(f"   Timestamp: {result.get('timestamp', 'N/A')}")
            if result.get("provider_id_used"):
                print(f"   Provider ID used: {result.get('provider_id_used')}")
        else:
            print("❌ INMAIL FAILED")
            print(f"   Error: {result.get('error', 'Unknown error')}")

            if "suggestions" in result:
                print("\n💡 Suggestions:")
                for suggestion in result["suggestions"]:
                    print(f"   - {suggestion}")

            if "solutions" in result:
                print("\n🔧 Solutions:")
                for solution in result["solutions"]:
                    print(f"   - {solution}")

    except Exception as e:
        print(f"❌ Exception during InMail: {e}")

    print_separator()

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python debug_linkedin.py <linkedin_identifier> [options]")
        print("\nOptions:")
        print("  --test-connection    Test sending a connection request")
        print("  --test-inmail        Test sending an InMail")
        print("  --test-both          Test both connection request and InMail")
        print("\nExamples:")
        print("  python debug_linkedin.py john-doe")
        print("  python debug_linkedin.py ACoAAABCDEF123456789")
        print("  python debug_linkedin.py demilade-adebanjo-554774202 --test-connection")
        print("  python debug_linkedin.py demilade-adebanjo-554774202 --test-inmail")
        print("  python debug_linkedin.py demilade-adebanjo-554774202 --test-both")
        return

    identifier = sys.argv[1]
    test_connection = "--test-connection" in sys.argv or "--test-both" in sys.argv
    test_inmail_flag = "--test-inmail" in sys.argv or "--test-both" in sys.argv

    print(f"LinkedIn Integration Debug Tool")
    print(f"Time: {datetime.now().isoformat()}")

    # Debug the identifier
    debug_identifier(identifier)

    # Test connection if requested
    if test_connection:
        test_connection_request(identifier)

    # Test InMail if requested
    if test_inmail_flag:
        test_inmail(identifier)

    # Show help if no tests requested
    if not test_connection and not test_inmail_flag:
        print("💡 Add --test-connection to test connection requests")
        print("💡 Add --test-inmail to test InMail functionality")
        print("💡 Add --test-both to test both features")

if __name__ == "__main__":
    main()
