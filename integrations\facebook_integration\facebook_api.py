"""
Facebook Messenger API Integration with OAuth 2.0 Flow
Handles authentication and messaging for Facebook Messenger Platform
"""

import requests
import json
import time
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime
import hashlib
import hmac
import urllib.parse
import secrets
import base64

class FacebookOAuth:
    """Facebook OAuth 2.0 implementation for page access token management"""

    def __init__(self, app_id: str, app_secret: str, redirect_uri: str):
        self.app_id = app_id
        self.app_secret = app_secret
        self.redirect_uri = redirect_uri
        self.base_url = "https://graph.facebook.com"
        self.oauth_url = "https://www.facebook.com/v18.0/dialog/oauth"

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def generate_auth_url(self, scopes: List[str] = None) -> Dict:
        """Generate OAuth authorization URL"""
        if not scopes:
            scopes = [
                "pages_messaging",
                "pages_manage_metadata",
                "pages_read_engagement",
                "pages_show_list"
            ]

        # Generate state parameter for security
        state = secrets.token_urlsafe(32)

        params = {
            "client_id": self.app_id,
            "redirect_uri": self.redirect_uri,
            "scope": ",".join(scopes),
            "response_type": "code",
            "state": state
        }

        auth_url = f"{self.oauth_url}?{urllib.parse.urlencode(params)}"

        return {
            "auth_url": auth_url,
            "state": state,
            "scopes": scopes
        }

    def exchange_code_for_token(self, code: str) -> Dict:
        """Exchange authorization code for access token"""
        try:
            token_url = f"{self.base_url}/v18.0/oauth/access_token"

            params = {
                "client_id": self.app_id,
                "client_secret": self.app_secret,
                "redirect_uri": self.redirect_uri,
                "code": code
            }

            response = requests.get(token_url, params=params)
            response.raise_for_status()

            token_data = response.json()

            if "access_token" in token_data:
                # Get long-lived token
                long_lived_token = self.get_long_lived_token(token_data["access_token"])
                return {
                    "success": True,
                    "access_token": token_data["access_token"],
                    "long_lived_token": long_lived_token.get("access_token"),
                    "expires_in": long_lived_token.get("expires_in"),
                    "token_type": token_data.get("token_type", "bearer")
                }
            else:
                return {"error": "No access token in response", "details": token_data}

        except Exception as e:
            self.logger.error(f"Token exchange error: {e}")
            return {"error": str(e)}

    def get_long_lived_token(self, short_lived_token: str) -> Dict:
        """Convert short-lived token to long-lived token"""
        try:
            url = f"{self.base_url}/v18.0/oauth/access_token"

            params = {
                "grant_type": "fb_exchange_token",
                "client_id": self.app_id,
                "client_secret": self.app_secret,
                "fb_exchange_token": short_lived_token
            }

            response = requests.get(url, params=params)
            response.raise_for_status()

            return response.json()

        except Exception as e:
            self.logger.error(f"Long-lived token error: {e}")
            return {"error": str(e)}

    def get_user_pages(self, user_access_token: str) -> Dict:
        """Get user's Facebook pages"""
        try:
            url = f"{self.base_url}/v18.0/me/accounts"

            params = {
                "access_token": user_access_token,
                "fields": "id,name,access_token,category,tasks"
            }

            response = requests.get(url, params=params)
            response.raise_for_status()

            return response.json()

        except Exception as e:
            self.logger.error(f"Get pages error: {e}")
            return {"error": str(e)}

    def get_page_access_token(self, user_access_token: str, page_id: str) -> Dict:
        """Get page access token for specific page"""
        try:
            pages_data = self.get_user_pages(user_access_token)

            if "data" in pages_data:
                for page in pages_data["data"]:
                    if page["id"] == page_id:
                        return {
                            "success": True,
                            "page_access_token": page["access_token"],
                            "page_name": page["name"],
                            "page_id": page["id"],
                            "tasks": page.get("tasks", [])
                        }

                return {"error": f"Page {page_id} not found or no access"}
            else:
                return {"error": "No pages found", "details": pages_data}

        except Exception as e:
            self.logger.error(f"Page token error: {e}")
            return {"error": str(e)}

class FacebookMessaging:
    """Facebook Messenger API with OAuth integration"""

    def __init__(self, config_path: str = "integrations/facebook_integration/config.json"):
        """Initialize Facebook Messenger API client"""
        self.config_path = config_path
        self.config = self._load_config()

        # OAuth configuration
        self.app_id = self.config.get("app_id")
        self.app_secret = self.config.get("app_secret")
        self.redirect_uri = self.config.get("redirect_uri", "http://localhost:5002/oauth/callback")

        # Page configuration
        self.page_access_token = self.config.get("page_access_token")
        self.page_id = self.config.get("page_id")
        self.verify_token = self.config.get("verify_token")

        # API configuration
        self.api_version = self.config.get("api_version", "v18.0")
        self.base_url = f"https://graph.facebook.com/{self.api_version}"

        # OAuth handler
        if self.app_id and self.app_secret:
            self.oauth = FacebookOAuth(self.app_id, self.app_secret, self.redirect_uri)
        else:
            self.oauth = None

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("messages_per_second", 10)

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}

    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")

    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        if not self.page_access_token:
            return {"error": "Page access token not configured"}

        self._rate_limit()

        url = f"{self.base_url}/{endpoint}"

        # Add access token to params
        if not params:
            params = {}
        params["access_token"] = self.page_access_token

        headers = {
            "Content-Type": "application/json"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=headers, json=data, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}

    def start_oauth_flow(self, scopes: List[str] = None) -> Dict:
        """Start OAuth 2.0 authorization flow"""
        if not self.oauth:
            return {"error": "OAuth not configured. Please set app_id and app_secret"}

        try:
            auth_data = self.oauth.generate_auth_url(scopes)

            # Save state for verification
            self.config["oauth_state"] = auth_data["state"]
            self._save_config()

            return {
                "success": True,
                "auth_url": auth_data["auth_url"],
                "state": auth_data["state"],
                "scopes": auth_data["scopes"],
                "message": "Visit the auth_url to authorize the application"
            }

        except Exception as e:
            self.logger.error(f"OAuth flow error: {e}")
            return {"error": str(e)}

    def handle_oauth_callback(self, code: str, state: str) -> Dict:
        """Handle OAuth callback and exchange code for tokens"""
        if not self.oauth:
            return {"error": "OAuth not configured"}

        try:
            # Verify state parameter
            saved_state = self.config.get("oauth_state")
            if not saved_state or saved_state != state:
                return {"error": "Invalid state parameter"}

            # Exchange code for token
            token_result = self.oauth.exchange_code_for_token(code)

            if token_result.get("success"):
                # Save user access token
                user_token = token_result["long_lived_token"] or token_result["access_token"]
                self.config["user_access_token"] = user_token
                self.config["token_expires_in"] = token_result.get("expires_in")

                # Get user's pages and select the first one
                pages_result = self.get_user_pages(user_token)

                if pages_result.get("success") and pages_result.get("pages"):
                    # Use the first page
                    page = pages_result["pages"][0]
                    self.config["page_access_token"] = page["access_token"]
                    self.config["page_id"] = page["id"]
                    self.config["page_name"] = page["name"]

                    # Update instance variables
                    self.page_access_token = page["access_token"]
                    self.page_id = page["id"]

                    self._save_config()

                    return {
                        "success": True,
                        "message": "OAuth authorization successful",
                        "page_name": page["name"],
                        "page_id": page["id"],
                        "access_token": token_result["access_token"],
                        "long_lived_token": token_result.get("long_lived_token"),
                        "expires_in": token_result.get("expires_in")
                    }
                else:
                    return {"error": "No pages found or unable to get page access tokens"}
            else:
                return {"error": token_result.get("error", "Token exchange failed")}

        except Exception as e:
            self.logger.error(f"OAuth callback error: {e}")
            return {"error": str(e)}

    def get_user_pages(self, user_access_token: str) -> Dict:
        """Get user's Facebook pages and their access tokens"""
        try:
            url = f"{self.base_url}/me/accounts"
            params = {
                "access_token": user_access_token,
                "fields": "id,name,access_token,category,tasks"
            }

            response = requests.get(url, params=params)
            response.raise_for_status()

            data = response.json()

            if "data" in data:
                pages = []
                for page in data["data"]:
                    # Only include pages where user has messaging permissions
                    if "MESSAGING" in page.get("tasks", []):
                        pages.append({
                            "id": page["id"],
                            "name": page["name"],
                            "access_token": page["access_token"],
                            "category": page.get("category", "Unknown")
                        })

                return {
                    "success": True,
                    "pages": pages,
                    "total_pages": len(pages)
                }
            else:
                return {"error": "No pages data in response"}

        except Exception as e:
            self.logger.error(f"Error getting user pages: {e}")
            return {"error": str(e)}

    def get_user_pages(self) -> Dict:
        """Get user's Facebook pages"""
        if not self.oauth:
            return {"error": "OAuth not configured"}

        user_token = self.config.get("user_access_token")
        if not user_token:
            return {"error": "User not authenticated. Please complete OAuth flow first"}

        try:
            pages_result = self.oauth.get_user_pages(user_token)

            if "data" in pages_result:
                return {
                    "success": True,
                    "pages": pages_result["data"],
                    "message": f"Found {len(pages_result['data'])} pages"
                }
            else:
                return {"error": pages_result.get("error", "Failed to get pages")}

        except Exception as e:
            self.logger.error(f"Get pages error: {e}")
            return {"error": str(e)}

    def select_page(self, page_id: str) -> Dict:
        """Select a page and get its access token"""
        if not self.oauth:
            return {"error": "OAuth not configured"}

        user_token = self.config.get("user_access_token")
        if not user_token:
            return {"error": "User not authenticated. Please complete OAuth flow first"}

        try:
            page_result = self.oauth.get_page_access_token(user_token, page_id)

            if page_result.get("success"):
                # Save page configuration
                self.config["page_id"] = page_result["page_id"]
                self.config["page_access_token"] = page_result["page_access_token"]
                self.config["page_name"] = page_result["page_name"]
                self._save_config()

                # Update instance variables
                self.page_id = page_result["page_id"]
                self.page_access_token = page_result["page_access_token"]

                return {
                    "success": True,
                    "page_id": page_result["page_id"],
                    "page_name": page_result["page_name"],
                    "message": f"Page '{page_result['page_name']}' selected successfully"
                }
            else:
                return {"error": page_result.get("error", "Failed to get page access token")}

        except Exception as e:
            self.logger.error(f"Select page error: {e}")
            return {"error": str(e)}

    def send_message(self, recipient_id: str, message: str, quick_replies: List[Dict] = None) -> Dict:
        """Send text message to user"""
        if not self.page_access_token:
            return {"error": "Page access token not configured. Please complete OAuth flow and select a page"}

        try:
            message_data = {
                "text": message
            }

            if quick_replies:
                message_data["quick_replies"] = quick_replies

            data = {
                "recipient": {"id": recipient_id},
                "message": message_data,
                "messaging_type": "RESPONSE"
            }

            result = self._make_request("POST", "me/messages", data)

            if "error" not in result:
                self.logger.info(f"Message sent successfully to {recipient_id}")
                return {
                    "success": True,
                    "message_id": result.get("message_id"),
                    "recipient_id": recipient_id
                }
            else:
                self.logger.error(f"Failed to send message: {result}")
                return {"error": result.get("error", {}).get("message", "Unknown error")}

        except Exception as e:
            self.logger.error(f"Send message error: {e}")
            return {"error": str(e)}

    def send_image_message(self, recipient_id: str, image_url: str, caption: str = None) -> Dict:
        """Send image message to user"""
        if not self.page_access_token:
            return {"error": "Page access token not configured"}

        try:
            attachment = {
                "type": "image",
                "payload": {
                    "url": image_url,
                    "is_reusable": True
                }
            }

            message_data = {
                "attachment": attachment
            }

            data = {
                "recipient": {"id": recipient_id},
                "message": message_data,
                "messaging_type": "RESPONSE"
            }

            result = self._make_request("POST", "me/messages", data)

            # Send caption as separate message if provided
            if caption and "error" not in result:
                self.send_message(recipient_id, caption)

            if "error" not in result:
                self.logger.info(f"Image sent successfully to {recipient_id}")
                return {"success": True, "message_id": result.get("message_id")}
            else:
                self.logger.error(f"Failed to send image: {result}")
                return {"error": result.get("error", {}).get("message", "Unknown error")}

        except Exception as e:
            self.logger.error(f"Send image error: {e}")
            return {"error": str(e)}

    def send_button_message(self, recipient_id: str, text: str, buttons: List[Dict]) -> Dict:
        """Send message with buttons"""
        if not self.page_access_token:
            return {"error": "Page access token not configured"}

        if len(buttons) > 3:
            return {"error": "Maximum 3 buttons allowed"}

        try:
            attachment = {
                "type": "template",
                "payload": {
                    "template_type": "button",
                    "text": text,
                    "buttons": buttons
                }
            }

            data = {
                "recipient": {"id": recipient_id},
                "message": {"attachment": attachment},
                "messaging_type": "RESPONSE"
            }

            result = self._make_request("POST", "me/messages", data)

            if "error" not in result:
                self.logger.info(f"Button message sent successfully to {recipient_id}")
                return {"success": True, "message_id": result.get("message_id")}
            else:
                self.logger.error(f"Failed to send button message: {result}")
                return {"error": result.get("error", {}).get("message", "Unknown error")}

        except Exception as e:
            self.logger.error(f"Send button message error: {e}")
            return {"error": str(e)}

    def send_bulk_messages(self, recipients: List[str], message: str, delay: float = 2.0) -> List[Dict]:
        """Send message to multiple recipients with delay"""
        if not self.page_access_token:
            return [{"error": "Page access token not configured"}]

        results = []

        for recipient_id in recipients:
            try:
                result = self.send_message(recipient_id, message)
                results.append({
                    "recipient_id": recipient_id,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })

                if delay > 0:
                    time.sleep(delay)

            except Exception as e:
                results.append({
                    "recipient_id": recipient_id,
                    "result": {"error": str(e)},
                    "timestamp": datetime.now().isoformat()
                })

        self.logger.info(f"Bulk messaging completed: {len(results)} messages processed")
        return results

    def get_user_profile(self, user_id: str, fields: List[str] = None) -> Dict:
        """Get user profile information"""
        if not self.page_access_token:
            return {"error": "Page access token not configured"}

        if not fields:
            fields = ["first_name", "last_name", "profile_pic"]

        try:
            params = {
                "fields": ",".join(fields)
            }

            result = self._make_request("GET", user_id, params=params)

            if "error" not in result:
                return {"success": True, "profile": result}
            else:
                return {"error": result.get("error", {}).get("message", "Unknown error")}

        except Exception as e:
            self.logger.error(f"Get user profile error: {e}")
            return {"error": str(e)}

    def set_page_settings(self, greeting_text: str = None, get_started_payload: str = "GET_STARTED") -> Dict:
        """Set page greeting and get started button"""
        if not self.page_access_token:
            return {"error": "Page access token not configured"}

        try:
            results = {}

            # Set greeting text
            if greeting_text:
                greeting_data = {
                    "greeting": [
                        {
                            "locale": "default",
                            "text": greeting_text
                        }
                    ]
                }

                greeting_result = self._make_request("POST", "me/messenger_profile", greeting_data)
                results["greeting"] = greeting_result

            # Set get started button
            get_started_data = {
                "get_started": {
                    "payload": get_started_payload
                }
            }

            get_started_result = self._make_request("POST", "me/messenger_profile", get_started_data)
            results["get_started"] = get_started_result

            return {"success": True, "results": results}

        except Exception as e:
            self.logger.error(f"Set page settings error: {e}")
            return {"error": str(e)}

    def verify_webhook(self, verify_token: str, challenge: str) -> Optional[str]:
        """Verify webhook for Facebook Messenger"""
        if verify_token == self.verify_token:
            return challenge
        return None

    def verify_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook signature"""
        if not self.app_secret:
            return False

        try:
            expected_signature = hmac.new(
                self.app_secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha1
            ).hexdigest()

            return hmac.compare_digest(f"sha1={expected_signature}", signature)
        except Exception as e:
            self.logger.error(f"Signature verification error: {e}")
            return False

    def is_configured(self) -> bool:
        """Check if Facebook Messenger is properly configured"""
        return bool(self.page_access_token and self.page_id)

    def is_oauth_configured(self) -> bool:
        """Check if OAuth is properly configured"""
        return bool(self.app_id and self.app_secret)

    def get_configuration_status(self) -> Dict:
        """Get detailed configuration status"""
        return {
            "oauth_configured": self.is_oauth_configured(),
            "page_configured": self.is_configured(),
            "app_id": bool(self.app_id),
            "app_secret": bool(self.app_secret),
            "page_access_token": bool(self.page_access_token),
            "page_id": bool(self.page_id),
            "verify_token": bool(self.verify_token),
            "user_authenticated": bool(self.config.get("user_access_token")),
            "page_name": self.config.get("page_name")
        }

    def update_config(self, **kwargs) -> Dict:
        """Update configuration"""
        try:
            for key, value in kwargs.items():
                if value:
                    self.config[key] = value
                    setattr(self, key, value)

            self._save_config()

            # Reinitialize OAuth if app credentials changed
            if "app_id" in kwargs or "app_secret" in kwargs:
                if self.app_id and self.app_secret:
                    self.oauth = FacebookOAuth(self.app_id, self.app_secret, self.redirect_uri)

            return {"success": True, "message": "Configuration updated successfully"}

        except Exception as e:
            self.logger.error(f"Config update error: {e}")
            return {"error": str(e)}

# Legacy FacebookAPI class for backward compatibility
class FacebookAPI(FacebookMessaging):
    """Legacy class for backward compatibility"""
    pass
    def __init__(self, config_path: str = "integrations/facebook_integration/config.json"):
        """Initialize Facebook Messenger API client"""
        self.config_path = config_path
        self.config = self._load_config()
        self.page_access_token = self.config.get("page_access_token")
        self.app_secret = self.config.get("app_secret")
        self.page_id = self.config.get("page_id")
        self.verify_token = self.config.get("verify_token")
        self.api_version = self.config.get("api_version", "v18.0")
        self.base_url = f"{self.config.get('base_url', 'https://graph.facebook.com')}/{self.api_version}"
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("messages_per_second", 10)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        if not self.page_access_token:
            return {"error": "Page access token not configured"}
        
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        # Add access token to params
        if not params:
            params = {}
        params["access_token"] = self.page_access_token
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=headers, json=data, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}
    
    def verify_webhook(self, verify_token: str, challenge: str) -> Optional[str]:
        """Verify webhook for Facebook Messenger"""
        if verify_token == self.verify_token:
            return challenge
        return None
    
    def verify_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook signature"""
        if not self.app_secret:
            return False
        
        expected_signature = hmac.new(
            self.app_secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha1
        ).hexdigest()
        
        return hmac.compare_digest(f"sha1={expected_signature}", signature)
    
    def send_text_message(self, recipient_id: str, message: str, 
                         quick_replies: List[Dict] = None) -> Dict:
        """Send text message to user"""
        if not self.page_access_token:
            return {"error": "Facebook Messenger not configured properly"}
        
        message_data = {
            "text": message
        }
        
        if quick_replies:
            message_data["quick_replies"] = quick_replies
        
        data = {
            "recipient": {"id": recipient_id},
            "message": message_data,
            "messaging_type": "RESPONSE"
        }
        
        result = self._make_request("POST", "me/messages", data)
        
        if "error" not in result:
            self.logger.info(f"Message sent successfully to {recipient_id}")
        else:
            self.logger.error(f"Failed to send message: {result}")
        
        return result
    
    def send_image_message(self, recipient_id: str, image_url: str, 
                          caption: str = None) -> Dict:
        """Send image message to user"""
        attachment = {
            "type": "image",
            "payload": {
                "url": image_url,
                "is_reusable": True
            }
        }
        
        message_data = {
            "attachment": attachment
        }
        
        data = {
            "recipient": {"id": recipient_id},
            "message": message_data,
            "messaging_type": "RESPONSE"
        }
        
        result = self._make_request("POST", "me/messages", data)
        
        # Send caption as separate message if provided
        if caption and "error" not in result:
            self.send_text_message(recipient_id, caption)
        
        if "error" not in result:
            self.logger.info(f"Image sent successfully to {recipient_id}")
        else:
            self.logger.error(f"Failed to send image: {result}")
        
        return result
    
    def send_button_message(self, recipient_id: str, text: str, 
                           buttons: List[Dict]) -> Dict:
        """Send message with buttons"""
        if len(buttons) > 3:
            return {"error": "Maximum 3 buttons allowed"}
        
        attachment = {
            "type": "template",
            "payload": {
                "template_type": "button",
                "text": text,
                "buttons": buttons
            }
        }
        
        data = {
            "recipient": {"id": recipient_id},
            "message": {"attachment": attachment},
            "messaging_type": "RESPONSE"
        }
        
        result = self._make_request("POST", "me/messages", data)
        
        if "error" not in result:
            self.logger.info(f"Button message sent successfully to {recipient_id}")
        else:
            self.logger.error(f"Failed to send button message: {result}")
        
        return result
    
    def send_generic_template(self, recipient_id: str, elements: List[Dict]) -> Dict:
        """Send generic template (carousel)"""
        if len(elements) > 10:
            return {"error": "Maximum 10 elements allowed"}
        
        attachment = {
            "type": "template",
            "payload": {
                "template_type": "generic",
                "elements": elements
            }
        }
        
        data = {
            "recipient": {"id": recipient_id},
            "message": {"attachment": attachment},
            "messaging_type": "RESPONSE"
        }
        
        result = self._make_request("POST", "me/messages", data)
        
        if "error" not in result:
            self.logger.info(f"Generic template sent successfully to {recipient_id}")
        else:
            self.logger.error(f"Failed to send generic template: {result}")
        
        return result
    
    def get_user_profile(self, user_id: str, fields: List[str] = None) -> Dict:
        """Get user profile information"""
        if not fields:
            fields = ["first_name", "last_name", "profile_pic"]
        
        params = {
            "fields": ",".join(fields)
        }
        
        return self._make_request("GET", user_id, params=params)
    
    def set_persistent_menu(self, menu_items: List[Dict]) -> Dict:
        """Set persistent menu for the page"""
        data = {
            "persistent_menu": [
                {
                    "locale": "default",
                    "composer_input_disabled": False,
                    "call_to_actions": menu_items
                }
            ]
        }
        
        return self._make_request("POST", "me/messenger_profile", data)
    
    def set_get_started_button(self, payload: str = "GET_STARTED") -> Dict:
        """Set get started button"""
        data = {
            "get_started": {
                "payload": payload
            }
        }
        
        return self._make_request("POST", "me/messenger_profile", data)
    
    def set_greeting_text(self, greeting: str) -> Dict:
        """Set greeting text"""
        data = {
            "greeting": [
                {
                    "locale": "default",
                    "text": greeting
                }
            ]
        }
        
        return self._make_request("POST", "me/messenger_profile", data)
    
    def send_bulk_messages(self, recipients: List[str], message: str, 
                          delay: float = 2.0) -> List[Dict]:
        """Send message to multiple recipients with delay"""
        results = []
        
        for recipient_id in recipients:
            result = self.send_text_message(recipient_id, message)
            results.append({
                "recipient_id": recipient_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })
            
            if delay > 0:
                time.sleep(delay)
        
        return results
    
    def send_template_message(self, recipient_id: str, template_name: str, 
                            **kwargs) -> Dict:
        """Send predefined template message"""
        templates = self.config.get("message_templates", {})
        
        if template_name not in templates:
            return {"error": f"Template '{template_name}' not found"}
        
        message = templates[template_name].format(**kwargs)
        
        # Add quick replies if available
        quick_replies = self.config.get("quick_replies", {}).get("main_menu")
        
        return self.send_text_message(recipient_id, message, quick_replies)
    
    def is_configured(self) -> bool:
        """Check if Facebook Messenger is properly configured"""
        return bool(self.page_access_token and self.page_id)
    
    def update_config(self, page_access_token: str = None, app_secret: str = None,
                     page_id: str = None, verify_token: str = None):
        """Update configuration"""
        if page_access_token:
            self.config["page_access_token"] = page_access_token
            self.page_access_token = page_access_token
        
        if app_secret:
            self.config["app_secret"] = app_secret
            self.app_secret = app_secret
        
        if page_id:
            self.config["page_id"] = page_id
            self.page_id = page_id
        
        if verify_token:
            self.config["verify_token"] = verify_token
            self.verify_token = verify_token
        
        self._save_config()
        self.logger.info("Facebook configuration updated")

# Example usage
if __name__ == "__main__":
    print("📘 Facebook Messenger Integration Example")
    print("=" * 50)

    # Example 1: Using new FacebookMessaging class with OAuth
    print("\n🔐 Using FacebookMessaging class with OAuth:")
    facebook = FacebookMessaging()

    # Check configuration status
    status = facebook.get_configuration_status()
    print(f"Configuration status: {status}")

    if facebook.is_oauth_configured():
        # Start OAuth flow
        print("\n🚀 Starting OAuth flow...")
        oauth_result = facebook.start_oauth_flow()
        if oauth_result.get("success"):
            print(f"Visit this URL to authorize: {oauth_result['auth_url']}")
            print("After authorization, use handle_oauth_callback() with the code")
        else:
            print(f"OAuth error: {oauth_result.get('error')}")

        # Example: Get user pages (after OAuth)
        # pages_result = facebook.get_user_pages()
        # print(f"User pages: {pages_result}")

        # Example: Select a page
        # select_result = facebook.select_page("PAGE_ID")
        # print(f"Page selection: {select_result}")

        if facebook.is_configured():
            # Send message
            # result = facebook.send_message("USER_PSID", "Hello from Facebook Messenger!")
            # print(f"Message result: {result}")

            # Send bulk messages
            # recipients = ["PSID1", "PSID2", "PSID3"]
            # bulk_results = facebook.send_bulk_messages(recipients, "Bulk message!")
            # print(f"Bulk results: {bulk_results}")

            # Set page settings
            # settings_result = facebook.set_page_settings("Welcome to our page!")
            # print(f"Page settings: {settings_result}")
            pass
        else:
            print("❌ Page not configured. Complete OAuth flow and select a page first.")
    else:
        print("❌ OAuth not configured. Please set app_id and app_secret in config.json")

    print("\n💡 To use this integration:")
    print("1. Create a Facebook App at developers.facebook.com")
    print("2. Add Messenger product to your app")
    print("3. Set app_id and app_secret in config.json")
    print("4. Start OAuth flow: facebook.start_oauth_flow()")
    print("5. Complete authorization and handle callback")
    print("6. Select a page: facebook.select_page(page_id)")
    print("7. Send messages: facebook.send_message(psid, 'Hello!')")

    # Example 2: Using legacy FacebookAPI class
    print("\n🔄 Using legacy FacebookAPI class:")
    legacy_facebook = FacebookAPI()

    if not legacy_facebook.is_configured():
        print("❌ Legacy Facebook Messenger not configured. Please update config.json with your credentials.")
    else:
        print("✅ Legacy Facebook Messenger configured")
        # Example: Send a test message (replace with actual user ID)
        # result = legacy_facebook.send_message("USER_PSID", "Hello from legacy Facebook Messenger!")
        # print(f"Legacy message result: {result}")
