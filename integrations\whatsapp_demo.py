#!/usr/bin/env python3
"""
WhatsApp Integration Demo
Demonstrates how to use WhatsApp messaging functionality
"""

import sys
import os
import time
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_whatsapp_messaging():
    """Demonstrate WhatsApp messaging functionality"""
    print("📱 WhatsApp Integration Demo")
    print("=" * 40)
    
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        
        # Initialize WhatsApp client
        whatsapp = WhatsAppMessaging()
        print("✅ WhatsApp client initialized")
        
        # Check connection status
        print("\n📡 Checking connection status...")
        status = whatsapp.check_connection_status()
        
        if status.get('connected'):
            print("✅ WhatsApp is connected!")
            print(f"   Connected accounts: {len(status.get('accounts', []))}")
            
            # Demo 1: Send a simple message
            print("\n📤 Demo 1: Send Simple Message")
            print("   (Replace with a real phone number to test)")
            
            # Example message sending (commented out for safety)
            """
            phone_number = "+**********"  # Replace with real number
            message = "Hello! This is a test message from the WhatsApp integration."
            
            result = whatsapp.send_message(phone_number, message)
            
            if result.get('success'):
                print(f"✅ Message sent successfully!")
                print(f"   Message ID: {result.get('message_id')}")
                print(f"   Method: {result.get('method')}")
            else:
                print(f"❌ Failed to send message: {result.get('error')}")
            """
            
            print("   Code example:")
            print("   result = whatsapp.send_message('+**********', 'Hello World!')")
            print("   if result.get('success'):")
            print("       print('Message sent!')")
            
            # Demo 2: Bulk messaging
            print("\n📤 Demo 2: Bulk Messaging")
            print("   (Replace with real phone numbers to test)")
            
            """
            recipients = [
                {"phone_number": "+**********", "name": "John"},
                {"phone_number": "+**********", "name": "Jane"},
                {"phone_number": "+1122334455", "name": "Bob"}
            ]
            
            message_template = "Hello {name}! This is a personalized bulk message."
            
            results = whatsapp.send_bulk_messages(recipients, message_template, delay=2.0)
            
            successful = sum(1 for r in results if r.get('result', {}).get('success'))
            total = len(results)
            
            print(f"✅ Bulk messaging completed: {successful}/{total} successful")
            """
            
            print("   Code example:")
            print("   recipients = [")
            print("       {'phone_number': '+**********', 'name': 'John'},")
            print("       {'phone_number': '+**********', 'name': 'Jane'}")
            print("   ]")
            print("   template = 'Hello {name}! This is a bulk message.'")
            print("   results = whatsapp.send_bulk_messages(recipients, template)")
            
            # Demo 3: Message templates
            print("\n📝 Demo 3: Using Message Templates")
            print("   Available templates from config:")
            
            config = whatsapp._load_config()
            templates = config.get('message_templates', {})
            
            for name, template in templates.items():
                print(f"   • {name}: {template[:50]}...")
            
            print("\n   Code example:")
            print("   template = whatsapp.config.get('message_templates', {}).get('welcome')")
            print("   result = whatsapp.send_message('+**********', template)")
            
        else:
            print("❌ WhatsApp is not connected")
            print("   Please scan the QR code first to connect your account")
            
            # Show how to authenticate
            print("\n🔐 Authentication Process:")
            print("   1. Generate QR code:")
            print("      auth_result = whatsapp.authenticate_account()")
            print("   2. Scan QR code with WhatsApp mobile app")
            print("   3. Check connection status:")
            print("      status = whatsapp.check_connection_status()")
            
            # Generate QR code for demonstration
            print("\n   Generating QR code now...")
            auth_result = whatsapp.authenticate_account()
            
            if auth_result.get('success') and auth_result.get('qr_required'):
                print("   ✅ QR code generated!")
                print("   📱 Scan the QR code to connect your WhatsApp account")
                
                # Save QR code for easy access
                if auth_result.get('qr_code'):
                    qr_file = 'demo_whatsapp_qr.html'
                    with open(qr_file, 'w', encoding='utf-8') as f:
                        f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <title>WhatsApp Demo QR Code</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; padding: 20px; background: #f0f0f0; }}
        .container {{ background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }}
        .qr-code {{ max-width: 100%; border: 3px solid #25D366; border-radius: 15px; margin: 20px 0; }}
        .instructions {{ text-align: left; background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 20px 0; }}
        .header {{ color: #25D366; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">📱 WhatsApp Demo - Connect Your Account</h1>
        <img src="{auth_result['qr_code']}" alt="WhatsApp QR Code" class="qr-code">
        
        <div class="instructions">
            <h3>📋 How to Connect:</h3>
            <ol>
                <li><strong>Open WhatsApp</strong> on your mobile phone</li>
                <li>Go to <strong>Settings → Linked Devices</strong></li>
                <li>Tap <strong>"Link a Device"</strong></li>
                <li><strong>Scan this QR code</strong> with your phone</li>
                <li>Your account will be connected automatically!</li>
            </ol>
        </div>
        
        <p><em>QR Code generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</em></p>
        <p style="color: #666; font-size: 0.9em;">This QR code expires after a few minutes. Refresh the page if needed.</p>
    </div>
</body>
</html>
                        """)
                    
                    print(f"   💾 QR code saved to: {qr_file}")
                    print(f"   🌐 Open in browser: file://{os.path.abspath(qr_file)}")
            
        # Show configuration options
        print("\n⚙️  Configuration Options:")
        print("   • Rate limiting: 1 request per second")
        print("   • Message templates: Available in config.json")
        print("   • Bulk messaging: Supported with delays")
        print("   • Media messages: Supported (images, documents)")
        print("   • QR code authentication: Automatic")
        
        # Show API endpoints
        print("\n🌐 API Endpoints (when server is running):")
        print("   • GET  /api/whatsapp/status - Check connection status")
        print("   • POST /api/whatsapp/authenticate - Generate QR code")
        print("   • POST /api/messaging/send - Send single message")
        print("   • POST /api/messaging/bulk - Send bulk messages")
        print("   • GET  /whatsapp/whatsapp_auth.html - Web interface")
        
        print("\n🎉 WhatsApp integration is ready to use!")
        print("   Connect your account and start sending messages!")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()

def show_api_examples():
    """Show API usage examples"""
    print("\n📚 API Usage Examples")
    print("=" * 30)
    
    print("1. Basic Message Sending:")
    print("""
from whatsapp_integration.whatsapp_api import WhatsAppMessaging

whatsapp = WhatsAppMessaging()
result = whatsapp.send_message("+**********", "Hello World!")

if result.get('success'):
    print(f"Message sent! ID: {result.get('message_id')}")
else:
    print(f"Failed: {result.get('error')}")
""")
    
    print("2. Bulk Messaging:")
    print("""
recipients = [
    {"phone_number": "+**********", "name": "John"},
    {"phone_number": "+**********", "name": "Jane"}
]

template = "Hello {name}! Welcome to our service."
results = whatsapp.send_bulk_messages(recipients, template, delay=2.0)

for result in results:
    phone = result['phone_number']
    success = result['result'].get('success', False)
    print(f"{phone}: {'✅' if success else '❌'}")
""")
    
    print("3. Connection Management:")
    print("""
# Check if connected
status = whatsapp.check_connection_status()
if status.get('connected'):
    print("Ready to send messages!")
else:
    # Generate QR code for authentication
    auth = whatsapp.authenticate_account()
    if auth.get('qr_required'):
        print("Scan QR code to connect")
""")

if __name__ == "__main__":
    demo_whatsapp_messaging()
    show_api_examples()
